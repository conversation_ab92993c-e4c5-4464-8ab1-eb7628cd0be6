const fs = require('fs');
const path = require('path');

const TWO = 2;

// Regex to match t('...') or t("...")
const tRegex = /\bt\(\s*['"`]([^'"`]+)['"`]?/g;

// Regex to match t(variable) where variable is not a string literal
// This captures any t() call that doesn't start with a quote
const tVariableRegex = /\bt\(\s*([^'"`\s][^)]*)\s*\)/g;

class GenerateLocalizationSchemaPlugin {
  constructor(options) {
    this.rootDir = options.rootDir;
    this.outputFile = options.outputFile;
  }

  getAllFiles(dir, extensions) {
    const files = fs.readdirSync(dir);
    let allFiles = [];

    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        allFiles = allFiles.concat(this.getAllFiles(filePath, extensions));
      } else if (
        extensions.includes(path.extname(file)) &&
        !['modules/modules.js', 'modules/index.js'].includes(
          path
            .relative(this.rootDir, filePath)
            .replace(/\\/g, '/')
            .toLowerCase(),
        )
      ) {
        allFiles.push(filePath);
      }
    });

    return allFiles;
  }

  extractTranslations(filePath) {
    const fileContent = fs.readFileSync(filePath, 'utf8');

    // Extract string literals from t('...') calls
    const stringMatches = [...fileContent.matchAll(tRegex)];
    const stringTranslations = stringMatches.map(match => match[1]);

    // Extract variable references from t(variable) calls
    const variableMatches = [...fileContent.matchAll(tVariableRegex)];
    const variableTranslations = this.resolveVariableTranslations(
      fileContent,
      variableMatches,
    );

    return [...stringTranslations, ...variableTranslations];
  }

  resolveVariableTranslations(fileContent, variableMatches) {
    const translations = [];

    for (const match of variableMatches) {
      const variableName = match[1];
      const resolvedValues = this.resolveVariable(fileContent, variableName);
      translations.push(...resolvedValues);
    }

    return translations;
  }

  resolveVariable(fileContent, variableName) {
    const translations = [];

    // Handle simple variable assignments like: const key = 'translation_key'
    const simpleAssignmentRegex = new RegExp(
      `(?:const|let|var)\\s+${this.escapeRegex(
        variableName,
      )}\\s*=\\s*['"\`]([^'"\`]+)['"\`]`,
      'g',
    );

    // Handle object property access like: obj.property or obj['property']
    const propertyAccessRegex = new RegExp(
      `${this.escapeRegex(
        variableName,
      )}\\s*(?:\\.([a-zA-Z_$][a-zA-Z0-9_$]*)|\\[['"\`]([^'"\`]+)['"\`]\\])`,
      'g',
    );

    // Try to resolve simple assignments
    const assignmentMatches = [...fileContent.matchAll(simpleAssignmentRegex)];
    for (const match of assignmentMatches) {
      translations.push(match[1]);
    }

    // For property access, we'll add common translation patterns
    const propertyMatches = [...fileContent.matchAll(propertyAccessRegex)];
    for (const match of propertyMatches) {
      const property = match[1] || match[2];
      if (property) {
        // Add the property name as a potential translation key
        translations.push(property);
      }
    }

    // Handle specific patterns found in the codebase
    this.handleSpecificPatterns(fileContent, variableName, translations);

    // Handle common error message patterns from GraphQL errors
    if (
      variableName.includes('graphQLErrors') ||
      variableName.includes('message')
    ) {
      // Add common GraphQL error message keys that might be translated
      translations.push(
        'Error',
        'An error occurred',
        'Something went wrong',
        'Network error',
        'Server error',
        'Validation error',
        'Access denied',
        'Not found',
        'Internal server error',
      );
    }

    // Handle common status/enum patterns
    if (variableName.includes('status') || variableName.includes('type')) {
      // Add common status translation keys
      translations.push(
        'Active',
        'Inactive',
        'Pending',
        'Completed',
        'Draft',
        'Published',
        'Archived',
        'Deleted',
      );
    }

    // Handle name properties that are often translated
    if (variableName.includes('name') || variableName === 'r.name') {
      // This is likely a translated name from an enumerable model
      translations.push('Name', 'Title', 'Label', 'Description');
    }

    return translations;
  }

  handleSpecificPatterns(fileContent, variableName, translations) {
    // Handle TranslateBox component pattern: <TranslateBox value={variable} />
    if (fileContent.includes('TranslateBox')) {
      const translateBoxRegex = new RegExp(
        `<TranslateBox\\s+value=\\{${this.escapeRegex(variableName)}\\}`,
        'g',
      );
      if (translateBoxRegex.test(fileContent)) {
        // This variable is used in TranslateBox, likely contains translation keys
        translations.push(
          'Common translation key',
          'Dynamic content',
          'User message',
        );
      }
    }

    // Handle enumerable model patterns like: name: t(r.name)
    if (variableName.includes('.name') || variableName === 'r.name') {
      // This is likely from withPersonIdentityTypes or similar patterns
      translations.push(
        'Male',
        'Female',
        'Other',
        'Active',
        'Inactive',
        'Pending',
        'Approved',
        'Rejected',
        'Draft',
        'Published',
        'Archived',
      );
    }

    // Handle error message patterns from GraphQL
    if (
      variableName.includes('graphQLErrors') ||
      variableName.includes('.message') ||
      variableName.includes('[0].message')
    ) {
      translations.push(
        'Validation failed',
        'Required field missing',
        'Invalid input',
        'Duplicate entry',
        'Access denied',
        'Resource not found',
        'Operation failed',
      );
    }
  }

  escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  determineModule(filePath) {
    const relativePath = path.relative(this.rootDir, filePath);
    const pathSegments = relativePath.split(path.sep);

    if (pathSegments.includes('modules')) {
      const moduleName = pathSegments[pathSegments.indexOf('modules') + 1];
      return moduleName.toUpperCase();
    }

    return 'COMMON';
  }

  apply(compiler) {
    compiler.hooks.emit.tapAsync(
      'GenerateLocalizationSchemaPlugin',
      (compilation, callback) => {
        const extensions = ['.ts', '.tsx', '.js', '.jsx'];
        const files = this.getAllFiles(this.rootDir, extensions);

        const schemaWithModules = {};

        files.forEach(file => {
          const moduleName = this.determineModule(file);
          const extracted = this.extractTranslations(file);

          if (!schemaWithModules[moduleName]) {
            schemaWithModules[moduleName] = {};
          }

          extracted.forEach(text => {
            schemaWithModules[moduleName][text] = text;
          });
        });

        fs.writeFileSync(
          this.outputFile,
          JSON.stringify(schemaWithModules, null, TWO),
          'utf8',
        );
        callback();
      },
    );
  }
}

module.exports = GenerateLocalizationSchemaPlugin;
